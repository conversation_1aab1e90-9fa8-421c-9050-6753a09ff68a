# LatentSync 1.6

Many people have reported that the teeth and lips generated by LatentSync 1.5 are blurry. To address this issue, we trained LatentSync 1.6 on 512 $\times$ 512 resolution videos. 

Notably, we did not make any changes to the model structure or training strategy; the only modification was upgrading the training dataset to 512 $\times$ 512 videos. Therefore, the current code is compatible with both LatentSync 1.5 and 1.6. To switch between versions, you only need to load the corresponding checkpoint and modify the `resolution` parameter in the U-Net config file.

## LatentSync 1.6 Demo

<table class="center">
  <tr style="font-weight: bolder;text-align:center;">
        <td width="33%"><b>Original video</b></td>
        <td width="33%"><b>Lip-synced video (v1.5)</b></td>
        <td width="33%"><b>Lip-synced video (v1.6)</b></td>
  </tr>
  <tr>
    <td>
      <video src=https://github.com/user-attachments/assets/2cbb7235-fc21-4f2f-80aa-78eca7d15706 controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/0f87979e-12c6-4b92-bc8a-6514ebea4fe7 controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/cc1f274f-1617-4d84-a5b2-6ef636166353 controls preload></video>
    </td>
  </tr>
  <tr>
    <td>
      <video src=https://github.com/user-attachments/assets/b778e3c3-ba25-455d-bdf3-d89db0aa75f4 controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/e3b5b8b9-786f-4ffe-a47c-8cedc42794f6 controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/ac791682-1541-4e6a-aa11-edd9427b977e controls preload></video>
    </td>
  </tr>
  <tr>
    <td>
      <video src=https://github.com/user-attachments/assets/6d4f4afd-6547-428d-8484-09dc53a19ecf controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/c490dc89-b271-4fcf-ab8b-fc69b8cb6cfd controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/b4723d08-c1d4-4237-8251-09c43eb77a6a controls preload></video>
    </td>
  </tr>
  <tr>
    <td>
      <video src=https://github.com/user-attachments/assets/fb4dc4c1-cc98-43dd-a211-1ff8f843fcfa controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/21f33871-0c7a-41ab-b1ef-c63ecff36ae5 controls preload></video>
    </td>
    <td>
      <video src=https://github.com/user-attachments/assets/7c6ca513-d068-4aa9-8a82-4dfd9063ac4e controls preload></video>
    </td>
  </tr>
</table>