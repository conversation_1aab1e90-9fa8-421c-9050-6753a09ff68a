# animation-1
- adapter_lora_scale: 1.0
  adapter_lora_path: "models/Motion_Module/v3_sd15_adapter.ckpt"
  dreambooth_path:   "models/DreamBooth_LoRA/realisticVisionV60B1_v51VAE.safetensors"

  inference_config: "configs/inference/inference-v3.yaml"
  motion_module:    "models/Motion_Module/v3_sd15_mm.ckpt"

  controlnet_config: "configs/inference/sparsectrl/latent_condition.yaml"
  controlnet_path:   "models/SparseCtrl/v3_sd15_sparsectrl_rgb.ckpt"

  seed: -1
  steps: 25
  guidance_scale: 8.5

  controlnet_image_indexs: [0]
  controlnet_images:
    - "__assets__/demos/image/RealisticVision_firework.png"

  prompt:
    - "closeup face photo of man in black clothes, night city street, bokeh, fireworks in background"
    - "closeup face photo of man in black clothes, night city street, bokeh, fireworks in background"
  n_prompt:
    - "worst quality, low quality, letterboxed"


# animation-2
- adapter_lora_scale: 1.0
  adapter_lora_path: "models/Motion_Module/v3_sd15_adapter.ckpt"
  dreambooth_path:   "models/DreamBooth_LoRA/realisticVisionV60B1_v51VAE.safetensors"

  inference_config: "configs/inference/inference-v3.yaml"
  motion_module:    "models/Motion_Module/v3_sd15_mm.ckpt"

  controlnet_config: "configs/inference/sparsectrl/latent_condition.yaml"
  controlnet_path:   "models/SparseCtrl/v3_sd15_sparsectrl_rgb.ckpt"

  seed: -1
  steps: 25
  guidance_scale: 8.5

  controlnet_image_indexs: [0]
  controlnet_images:
    - "__assets__/demos/image/RealisticVision_sunset.png"

  prompt:
    - "masterpiece, bestquality, highlydetailed, ultradetailed, sunset, orange sky, warm lighting, fishing boats, ocean waves, seagulls, rippling water, wharf, silhouette, serene atmosphere, dusk, evening glow, golden hour, coastal landscape, seaside scenery"
    - "masterpiece, bestquality, highlydetailed, ultradetailed, sunset, orange sky, warm lighting, fishing boats, ocean waves, seagulls, rippling water, wharf, silhouette, serene atmosphere, dusk, evening glow, golden hour, coastal landscape, seaside scenery"
  n_prompt:
    - "worst quality, low quality, letterboxed"
